<div class="re-quick-modal">
  <div class="re-head-div">
    <div class="re-head-1">
      <h3>{{ data?.fromAudience ? data?.headerText : "Quick Filter" }}</h3>
      <img
        class="re-closed"
        width="22"
        src="assets/images/quick-filters/close-square.svg"
        alt="close"
        mat-dialog-close
        *ngIf="!isShare"
      />
      <button
        class="re-back-button"
        *ngIf="isShare"
        (click)="backToApplyFilterMenu()"
      >
        <i class="fa fa-long-arrow-left" aria-hidden="true"></i> Back
      </button>
    </div>
    <div class="re-head-2" *ngIf="!isShare">
      <button
        type="button"
        class="btn"
        [class.btn-active]="tabIndex == 1"
        (click)="tabChange(1, true)"
      >
        Parameters ({{ filterParameters?.length }})
      </button>
      |
      <button
        type="button"
        class="btn"
        [class.btn-active]="tabIndex == 2"
        (click)="tabChange(2, true)"
      >
        Filter Values
      </button>
      |
      <button
        type="button"
        class="btn"
        [class.btn-active]="tabIndex == 3"
        (click)="tabChange(3, true)"
      >
        Save & Configure
      </button>
    </div>
  </div>

  <div class="re-parameter-div" *ngIf="step1">
    <div
      class="re-search-div"
      *ngIf="!this.isIndividualDashboard && !this.isComparisonDashboard"
    >
      <i class="fa fa-search"></i>
      <input
        #search
        type="text"
        class="form-control"
        name=""
        id="searchInputOne"
        placeholder="Search filter values"
        [(ngModel)]="fieldFind"
        (input)="searchFiltersOne()"
      />
    </div>

    <mat-dialog-content>
      <!-- filter with group name -->
      <div *ngFor="let group of filterGroup">
        <ng-container *ngIf="group?.show">
          <div class="re-checktitle">{{ group?.name }}</div>
        </ng-container>
        <ng-container
          *ngFor="let filter of filterParameters; let index = index"
        >
          <ng-container
            *ngIf="
              filter?.filterGroup &&
              group?.name == filter?.filterGroup &&
              filter?.show
            "
          >
            <div class="checkboxlist" *ngIf="!filter?.isColumns">
              <!-- <label class="checkboxbtn" [class.re-disabled]="viewMode">
                {{ filter.label }}
                <span
                  *ngIf="filter?.value == 'sellerAppt'"
                  pTooltip="Leads with previously booked seller appointments"
                  tooltipPosition="bottom"
                >
                  <i class="fa fa-question-circle"></i>
                </span>
                <input
                  type="checkbox"
                  name=""
                  [value]="filter"
                  (change)="checkedValue($event, index)"
                  [checked]="filter.checked"
                />
                <span class="re-checkmark"></span>
              </label> -->
              <label class="checkboxbtn" [class.re-disabled]="viewMode">
                {{ filter?.label }}
                <span
                  *ngIf="filter?.isTooltip"
                  [pTooltip]="filter?.tooltipMsg"
                  tooltipPosition="bottom"
                >
                  <i class="fa fa-question-circle"></i>
                </span>
                <input
                  type="checkbox"
                  name=""
                  [value]="filter"
                  (change)="checkedValue($event, index)"
                  [checked]="filter.checked"
                  [disabled]="data?.fromAudience && filter?.value == 'email'"
                />
                <span class="re-checkmark"></span>
              </label>
            </div>
          </ng-container>
        </ng-container>
      </div>
      <!-- Data to Display -->
      <div class="re-display-checktitle" *ngIf="isDataToDisplay">
        Data to Display
      </div>
      <div *ngFor="let group of filterShowGroup">
        <ng-container *ngIf="group?.show">
          <div class="re-checktitle">{{ group?.name }}</div>
        </ng-container>
        <ng-container
          *ngFor="let filter of filterParameters; let index = index"
        >
          <ng-container
            *ngIf="
              filter?.filterGroup &&
              group?.name == filter?.filterGroup &&
              filter?.show
            "
          >
            <div class="checkboxlist" *ngIf="filter?.isColumns">
              <label class="checkboxbtn" [class.re-disabled]="viewMode">
                {{ filter?.label }}
                <span
                  *ngIf="filter?.isTooltip"
                  [pTooltip]="filter?.tooltipMsg"
                  tooltipPosition="bottom"
                >
                  <i class="fa fa-question-circle"></i>
                </span>
                <input
                  type="checkbox"
                  name=""
                  [value]="filter"
                  (change)="checkedValue($event, index)"
                  [checked]="filter.checked"
                  [disabled]="data?.fromAudience && filter?.value == 'email'"
                />
                <span class="re-checkmark"></span>
              </label>
            </div>
          </ng-container>
        </ng-container>
      </div>
      <!-- filter without group name -->
      <div *ngIf="filterGroup.length == 0">
        <ng-container
          *ngFor="let filter of filterParameters; let index = index"
        >
          <ng-container *ngIf="!filter?.isColumns">
            <div
              class="checkboxlist"
              [class.re-disabled-text]="filter.disabled"
            >
              <label
                class="checkboxbtn"
                [class.re-disabled]="viewMode"
                [class.re-disabled-text]="filter.disabled"
              >
                {{ filter.label }}
                <input
                  type="checkbox"
                  name=""
                  [value]="filter"
                  (change)="checkedValue($event, index)"
                  [checked]="filter.checked"
                  [disabled]="data?.fromAudience && filter?.value == 'email'"
                />
                <span class="re-checkmark"></span>
              </label>
            </div>
          </ng-container>
        </ng-container>
      </div>
    </mat-dialog-content>
  </div>

  <div class="re-parameter-div" *ngIf="step2">
    <div class="re-search-div">
      <i class="fa fa-search"></i>
      <input
        #search
        type="text"
        class="form-control"
        name=""
        id="searchInputTwo"
        placeholder="Search filter values"
        [(ngModel)]="selectFieldFind"
        (input)="searchFiltersTwo()"
      />
    </div>

    <mat-dialog-content>
      <div class="re-valuesdiv" *ngFor="let group of selectedFilterGroup">
        <ng-container *ngIf="group?.show">
          <div class="re-checktitle">{{ group?.name }}</div>
        </ng-container>
        <ng-container *ngFor="let filter of selectedFilter; let index = index">
          <ng-container
            *ngIf="
              filter?.filterGroup &&
              group?.name == filter?.filterGroup &&
              filter?.show
            "
          >
            <div class="form-group" *ngIf="!filter?.isColumns">
              <label class="re-labels">{{ filter?.label }}</label>
              <div class="row">
                <div
                  class="col-12 col-md-4"
                  *ngIf="filter?.value != 'leadCustomQuestions'"
                >
                  <select
                    class="form-control"
                    [(ngModel)]="filter.selectedOperator"
                    [class.re-disabled]="viewMode"
                    (change)="changeSelectedOperator(filter, index)"
                  >
                    <option
                      *ngFor="let opt of filter?.operator"
                      [value]="opt?.value"
                    >
                      {{ opt?.label }}
                    </option>
                  </select>
                  <div class="re-error mb-2" *ngIf="filter?.operatorError">
                    <span> *Please select one option. </span>
                  </div>
                </div>
                <ng-container [ngSwitch]="filter?.optionsType">
                  <!-- SINGLE SELECT -->
                  <ng-container *ngSwitchCase="'SELECT'">
                    <div class="col-12 col-md-8">
                      <select
                        class="form-control"
                        [(ngModel)]="filter.selectedOption"
                        [class.re-disabled]="viewMode"
                        (change)="changeValidation(index)"
                        [disabled]="
                          data?.fromAudience && filter?.value == 'email'
                        "
                      >
                        <option
                          *ngFor="let option of filter?.options"
                          [value]="option?.value"
                        >
                          {{ option?.label }}
                        </option>
                      </select>
                      <div class="re-error mb-2" *ngIf="filter?.error == true">
                        <span> *Please select one option. </span>
                      </div>
                    </div>
                  </ng-container>

                  <!-- MULTIPLE SELECT -->
                  <ng-container *ngSwitchCase="'MULTI-SELECT'">
                    <div class="col-12 col-md-8">
                      <p-multiSelect
                        #select
                        class="re-custom-field"
                        [name]="filter.value + '-' + index"
                        [id]="filter.value + '-' + index"
                        [placeholder]="'Select ' + filter?.label + '(s)'"
                        [options]="filter.options"
                        [(ngModel)]="filter.selectedOption"
                        appendTo="body"
                        [class.re-disabled]="viewMode"
                        (onChange)="changeValidation(index)"
                        (onFilter)="fetchFilteredOptions($event, filter, index)"
                      ></p-multiSelect>
                      <div class="re-error mb-2" *ngIf="filter?.error == true">
                        <span> *Please select at least one option. </span>
                      </div>
                    </div>

                    <div
                      [ngClass]="
                        filter?.selectedOperator == 'is'
                          ? 'col-12 col-md-8'
                          : 'col-12 col-md-4'
                      "
                      *ngIf="
                        filter?.selectedOperator == 'is' &&
                        filter?.conditionType &&
                        filter?.conditionType.length
                      "
                    >
                      <select
                        [(ngModel)]="filter.selectedCondition"
                        class="form-control"
                        [class.re-disabled]="viewMode"
                      >
                        <ng-container
                          *ngFor="let option of filter?.conditionType"
                        >
                          <option [value]="option.value">
                            {{ option?.label }}
                          </option>
                        </ng-container>
                      </select>
                    </div>
                  </ng-container>

                  <!-- DATE-RANGE -->
                  <!-- only for lead filter -->
                  <ng-container *ngSwitchCase="'DATE-RANGE'">
                    <div
                      [ngClass]="
                        filter?.selectedOperator == 'between'
                          ? 'col-12 col-md-4'
                          : 'col-12 col-md-8'
                      "
                      *ngIf="filter?.selectedOperator != 'is'"
                    >
                      <p-calendar
                        class="re-custom-date"
                        [(ngModel)]="filter.minVal"
                        styleClass="ngCalendarClass"
                        appendTo="body"
                        [showIcon]="true"
                        [icon]="'fa fa-calendar'"
                        placeholder="Start Date"
                        [class.re-disabled]="viewMode"
                        (onSelect)="changeValidation(index)"
                      ></p-calendar>
                      <div
                        class="re-error mb-2"
                        *ngIf="filter?.error == true && filter?.errorStartDate"
                      >
                        <span> {{ filter?.rangeErrorMsg }} </span>
                      </div>
                    </div>

                    <div
                      class="col-12 col-md-4"
                      *ngIf="filter?.selectedOperator == 'between'"
                    >
                      <p-calendar
                        class="re-custom-date"
                        [(ngModel)]="filter.maxVal"
                        styleClass="ngCalendarClass"
                        appendTo="body"
                        [showIcon]="true"
                        [icon]="'fa fa-calendar'"
                        placeholder="End Date"
                        [minDate]="filter.minVal"
                        [class.re-disabled]="viewMode"
                        (onSelect)="changeValidation(index)"
                      ></p-calendar>
                      <div
                        class="re-error mb-2"
                        *ngIf="filter?.error == true && filter?.errorEndDate"
                      >
                        <span> {{ filter?.rangeErrorMsg }} </span>
                      </div>
                    </div>

                    <div
                      [ngClass]="
                        filter?.selectedOperator == 'is'
                          ? 'col-12 col-md-8'
                          : 'col-12 col-md-4'
                      "
                      *ngIf="
                        filter?.selectedOperator == 'is' &&
                        filter?.conditionType &&
                        filter?.conditionType.length
                      "
                    >
                      <select
                        [(ngModel)]="filter.selectedCondition"
                        class="form-control"
                        [class.re-disabled]="viewMode"
                      >
                        <ng-container
                          *ngFor="let option of filter?.conditionType"
                        >
                          <option [value]="option.value">
                            {{ option?.label }}
                          </option>
                        </ng-container>
                      </select>
                    </div>
                  </ng-container>

                  <!-- RANGE -->
                  <ng-container *ngSwitchCase="'RANGE'">
                    <div
                      [ngClass]="
                        filter?.selectedOperator == 'between'
                          ? 'col-12 col-md-4'
                          : 'col-12 col-md-8'
                      "
                    >
                      <input
                        *ngIf="!filter.isPrice"
                        class="form-control"
                        type="number"
                        placeholder="{{
                          filter?.selectedOperator == 'between'
                            ? 'Min'
                            : 'Value'
                        }}"
                        [(ngModel)]="filter.minVal"
                        [class.re-disabled]="viewMode"
                        onkeypress="return (event.charCode == 8) ? null : event.charCode >= 48 && event.charCode <= 57"
                        (change)="changeValidation(index)"
                      />
                      <input
                        *ngIf="filter.isPrice"
                        class="form-control"
                        type="text"
                        [(ngModel)]="filter.minVal"
                        [class.re-disabled]="viewMode"
                        (blur)="transformAmount($event)"
                        (change)="changeValidation(index)"
                        placeholder="$xx.xx"
                        mask="separator.2"
                        [thousandSeparator]="','"
                        [decimalMarker]="'.'"
                        prefix="$ "
                        [dropSpecialCharacters]="true"
                      />
                      <div
                        class="re-error mb-2"
                        *ngIf="filter?.error == true && filter?.errorMinVal"
                      >
                        {{ filter?.rangeErrorMsg }}
                      </div>
                    </div>
                    <div
                      class="col-12 col-md-4"
                      *ngIf="filter?.selectedOperator == 'between'"
                    >
                      <input
                        *ngIf="!filter.isPrice"
                        class="form-control"
                        type="number"
                        placeholder="Max"
                        [(ngModel)]="filter.maxVal"
                        [class.re-disabled]="viewMode"
                        onkeypress="return (event.charCode == 8) ? null : event.charCode >= 48 && event.charCode <= 57"
                        (change)="changeValidation(index)"
                      />
                      <input
                        *ngIf="filter.isPrice"
                        class="form-control"
                        type="text"
                        placeholder="Max"
                        [(ngModel)]="filter.maxVal"
                        [class.re-disabled]="viewMode"
                        (blur)="transformAmount($event)"
                        (change)="changeValidation(index)"
                        placeholder="$xx.xx"
                        mask="separator.2"
                        [thousandSeparator]="','"
                        [decimalMarker]="'.'"
                        prefix="$ "
                        [dropSpecialCharacters]="true"
                      />
                      <div
                        class="re-error mb-2"
                        *ngIf="filter?.error == true && filter?.errorMaxVal"
                      >
                        {{ filter?.rangeErrorMsg }}
                      </div>
                    </div>
                  </ng-container>

                  <!-- TIME-RANGE -->
                  <ng-container *ngSwitchCase="'TIME-RANGE'">
                    <div class="col-12 col-md-4">
                      <input
                        class="form-control"
                        type="number"
                        placeholder="{{
                          filter?.selectedOperator == 'between'
                            ? 'Min'
                            : 'Value'
                        }}"
                        min="0"
                        [(ngModel)]="filter.selectedOption"
                        [class.re-disabled]="viewMode"
                        onkeypress="return (event.charCode == 8) ? null : event.charCode >= 48 && event.charCode <= 57"
                        (change)="changeValidation(index)"
                      />
                      <div
                        class="re-error mb-2"
                        *ngIf="filter?.error == true && filter?.errorMinVal"
                      >
                        {{ filter.rangeErrorMsg }}
                      </div>
                    </div>

                    <div class="col-12 col-md-4">
                      <select
                        [(ngModel)]="filter.selectedCondition"
                        class="form-control"
                        [class.re-disabled]="viewMode"
                        (change)="changeValidation(index)"
                      >
                        <ng-container
                          *ngFor="let option of filter?.conditionType"
                        >
                          <option [value]="option.value">
                            {{ option?.label }}
                          </option>
                        </ng-container>
                      </select>
                      <div
                        class="re-error mb-2"
                        *ngIf="filter?.error == true && filter?.conditionError"
                      >
                        {{ filter.rangeErrorMsg }}
                      </div>
                    </div>
                  </ng-container>
                  <!-- TIME-RANGE-ZERO -->
                  <ng-container *ngSwitchCase="'TIME-RANGE-ZERO'">
                    <div class="col-12 col-md-4">
                      <input
                        class="form-control"
                        type="text"
                        placeholder="Min"
                        [(ngModel)]="filter.selectedOption"
                        [class.re-disabled]="viewMode"
                        onkeypress="return (event.charCode == 8) ? null : event.charCode >= 48 && event.charCode <= 57"
                        (change)="changeValidation(index)"
                      />
                      <div
                        class="re-error mb-2"
                        *ngIf="filter?.error == true && filter?.errorMinVal"
                      >
                        {{ filter.rangeErrorMsg }}
                      </div>
                    </div>

                    <div class="col-12 col-md-4">
                      <select
                        [(ngModel)]="filter.selectedCondition"
                        class="form-control"
                        [class.re-disabled]="viewMode"
                        (change)="changeValidation(index)"
                      >
                        <ng-container
                          *ngFor="let option of filter?.conditionType"
                        >
                          <option [value]="option.value">
                            {{ option?.label }}
                          </option>
                        </ng-container>
                      </select>
                      <div
                        class="re-error mb-2"
                        *ngIf="filter?.error == true && filter?.conditionError"
                      >
                        {{ filter.rangeErrorMsg }}
                      </div>
                    </div>
                  </ng-container>

                  <!-- CUSTOM QUESTION -->
                  <ng-container *ngSwitchCase="'QUESTION'">
                    <ng-container
                      *ngFor="
                        let master of filter.masterOptions;
                        let index = index
                      "
                    >
                      <div class="form-group col-12">
                        <label class="re-labels">{{ master?.label }}</label>
                        <div class="row">
                          <div class="col-12 col-md-4">
                            <select
                              class="form-control"
                              [(ngModel)]="master.selectedOperator"
                              [class.re-disabled]="viewMode"
                            >
                              <option
                                *ngFor="let opt of master?.operator"
                                [value]="opt?.value"
                              >
                                {{ opt?.label }}
                              </option>
                            </select>
                          </div>

                          <ng-container [ngSwitch]="master?.type">
                            <!-- CHECK_LIST -->
                            <ng-container *ngSwitchCase="'CHECK_LIST'">
                              <div class="col-12 col-md-8">
                                <p-multiSelect
                                  #select
                                  class="re-custom-field"
                                  [name]="master.value + '-' + index"
                                  [id]="master.value + '-' + index"
                                  [placeholder]="
                                    'Select ' + master?.label + '(s)'
                                  "
                                  [options]="master.options"
                                  [(ngModel)]="master.selectedOption"
                                  appendTo="body"
                                  [virtualScroll]="true"
                                  [virtualScrollItemSize]="43"
                                  [class.re-disabled]="viewMode"
                                ></p-multiSelect>
                              </div>
                            </ng-container>

                            <!-- RADIO_BUTTON -->
                            <ng-container *ngSwitchCase="'RADIO_BUTTON'">
                              <div class="col-12 col-md-8">
                                <select
                                  class="form-control"
                                  [(ngModel)]="master.selectedOption"
                                  [class.re-disabled]="viewMode"
                                >
                                  <option
                                    *ngFor="let option of master?.options"
                                    [value]="option?.value"
                                  >
                                    {{ option?.label }}
                                  </option>
                                </select>
                              </div>
                            </ng-container>

                            <!-- DROPDOWN -->
                            <ng-container *ngSwitchCase="'DROPDOWN'">
                              <!-- <div class="col-12 col-md-8">
                                <p-multiSelect
                                  #select
                                  class="re-custom-field"
                                  [name]="master.value + '-' + index"
                                  [id]="master.value + '-' + index"
                                  [placeholder]="
                                    'Select ' + master?.label + '(s)'
                                  "
                                  [options]="master.options"
                                  [(ngModel)]="master.selectedOption"
                                  appendTo="body"
                                  [virtualScroll]="true"
                                  [virtualScrollItemSize]="43"
                                  [class.re-disabled]="viewMode"
                                ></p-multiSelect>
                              </div> -->

                              <div class="col-12 col-md-8">
                                <select
                                  class="form-control"
                                  [(ngModel)]="master.selectedOption"
                                  [class.re-disabled]="viewMode"
                                >
                                  <option
                                    *ngFor="let option of master?.options"
                                    [value]="option?.value"
                                  >
                                    {{ option?.label }}
                                  </option>
                                </select>
                              </div>
                            </ng-container>

                            <!-- NUMBER -->
                            <ng-container *ngSwitchCase="'NUMBER'">
                              <div
                                [ngClass]="
                                  master?.selectedOperator == 'between'
                                    ? 'col-12 col-md-4'
                                    : 'col-12 col-md-8'
                                "
                              >
                                <input
                                  class="form-control"
                                  type="number"
                                  placeholder="{{
                                    master?.selectedOperator == 'between'
                                      ? 'Min'
                                      : 'Value'
                                  }}"
                                  min="0"
                                  [(ngModel)]="master.minVal"
                                  [class.re-disabled]="viewMode"
                                  onkeypress="return (event.charCode == 8) ? null : event.charCode >= 48 && event.charCode <= 57"
                                />
                              </div>
                              <div
                                class="col-12 col-md-4"
                                *ngIf="master?.selectedOperator == 'between'"
                              >
                                <input
                                  class="form-control"
                                  type="number"
                                  placeholder="Max"
                                  min="0"
                                  [(ngModel)]="master.maxVal"
                                  [class.re-disabled]="viewMode"
                                  onkeypress="return (event.charCode == 8) ? null : event.charCode >= 48 && event.charCode <= 57"
                                />
                              </div>
                            </ng-container>

                            <!-- CURRENCY -->
                            <ng-container *ngSwitchCase="'CURRENCY'">
                              <div
                                [ngClass]="
                                  master?.selectedOperator == 'between'
                                    ? 'col-12 col-md-4'
                                    : 'col-12 col-md-8'
                                "
                              >
                                <input
                                  class="form-control"
                                  type="number"
                                  placeholder="{{
                                    master?.selectedOperator == 'between'
                                      ? 'Min'
                                      : 'Value'
                                  }}"
                                  min="0"
                                  [(ngModel)]="master.minVal"
                                  [class.re-disabled]="viewMode"
                                  onkeypress="return (event.charCode == 8) ? null : event.charCode >= 48 && event.charCode <= 57"
                                />
                              </div>
                              <div
                                class="col-12 col-md-4"
                                *ngIf="master?.selectedOperator == 'between'"
                              >
                                <input
                                  class="form-control"
                                  type="number"
                                  placeholder="Max"
                                  min="0"
                                  [(ngModel)]="master.maxVal"
                                  [class.re-disabled]="viewMode"
                                  onkeypress="return (event.charCode == 8) ? null : event.charCode >= 48 && event.charCode <= 57"
                                />
                              </div>
                            </ng-container>

                            <!-- TEXT -->
                            <ng-container *ngSwitchCase="'TEXT'">
                              <!-- <div class="col-12 col-md-8">
                                <input
                                  class="form-control"
                                  type="text"
                                  placeholder="Write answer"
                                  [(ngModel)]="master.selectedOption"
                                  [class.re-disabled]="viewMode"
                                />
                              </div> -->

                              <div class="col-12 col-md-8">
                                <select
                                  class="form-control"
                                  [(ngModel)]="master.selectedOption"
                                  [class.re-disabled]="viewMode"
                                >
                                  <option
                                    *ngFor="let option of master?.options"
                                    [value]="option?.value"
                                  >
                                    {{ option?.label }}
                                  </option>
                                </select>
                              </div>
                            </ng-container>

                            <!-- MULTILINE_TEXT -->
                            <ng-container *ngSwitchCase="'MULTILINE_TEXT'">
                              <!-- <div class="col-12 col-md-8">
                                <textarea
                                  class="form-control"
                                  type="text"
                                  placeholder="Write answer"
                                  [(ngModel)]="master.selectedOption"
                                  [class.re-disabled]="viewMode"
                                ></textarea>
                              </div> -->

                              <div class="col-12 col-md-8">
                                <select
                                  class="form-control"
                                  [(ngModel)]="master.selectedOption"
                                  [class.re-disabled]="viewMode"
                                >
                                  <option
                                    *ngFor="let option of master?.options"
                                    [value]="option?.value"
                                  >
                                    {{ option?.label }}
                                  </option>
                                </select>
                              </div>
                            </ng-container>

                            <!-- DATE -->
                            <ng-container *ngSwitchCase="'DATE'">
                              <div
                                [ngClass]="
                                  master?.selectedOperator == 'between'
                                    ? 'col-12 col-md-4'
                                    : 'col-12 col-md-8'
                                "
                                *ngIf="master?.selectedOperator != 'is'"
                              >
                                <p-calendar
                                  class="re-custom-date"
                                  [(ngModel)]="master.minVal"
                                  styleClass="ngCalendarClass"
                                  appendTo="body"
                                  [showIcon]="true"
                                  [icon]="'fa fa-calendar'"
                                  placeholder="Start Date"
                                  [class.re-disabled]="viewMode"
                                ></p-calendar>
                              </div>

                              <div
                                class="col-12 col-md-4"
                                *ngIf="master?.selectedOperator == 'between'"
                              >
                                <p-calendar
                                  class="re-custom-date"
                                  [(ngModel)]="master.maxVal"
                                  styleClass="ngCalendarClass"
                                  appendTo="body"
                                  [showIcon]="true"
                                  [icon]="'fa fa-calendar'"
                                  placeholder="End Date"
                                  [minDate]="master.minVal"
                                  [class.re-disabled]="viewMode"
                                ></p-calendar>
                              </div>
                            </ng-container>
                          </ng-container>
                        </div>
                      </div>
                    </ng-container>
                  </ng-container>

                  <!-- MULTIPLE SELECT WITH TITLE -->
                  <ng-container *ngSwitchCase="'MULTI-SELECT-TITLE'">
                    <div class="col-12 col-md-8">
                      <p-multiSelect
                        #select
                        class="re-custom-field"
                        [name]="filter.value + '-' + index"
                        [id]="filter.value + '-' + index"
                        [placeholder]="'Select ' + filter?.label + '(s)'"
                        [options]="filter.options"
                        [(ngModel)]="filter.selectedOption"
                        appendTo="body"
                        [class.re-disabled]="viewMode"
                        [group]="true"
                        (onChange)="changeValidation(index)"
                      >
                        <ng-template let-group pTemplate="group">
                          <div>
                            <span
                              [class]="
                                group.value == 'callConnectedStatus'
                                  ? 're-green'
                                  : group.value == 'callNotConnectedStatus'
                                  ? 're-red'
                                  : ''
                              "
                              >{{ group.label }}</span
                            >
                          </div>
                        </ng-template>
                      </p-multiSelect>
                      <div class="re-error mb-2" *ngIf="filter?.error == true">
                        <span> *Please select at least one option. </span>
                      </div>
                    </div>
                  </ng-container>

                  <ng-container *ngSwitchCase="'DATE-SCORE'">
                    <div
                      class="col-12 col-md-5"
                      *ngIf="filter?.selectedOperator == 'is'"
                    >
                      <p-calendar
                        class="re-custom-date1"
                        [(ngModel)]="filter.minVal"
                        styleClass="ngCalendarClass"
                        appendTo="body"
                        [showIcon]="true"
                        [icon]="'fa fa-calendar'"
                        placeholder="Start Date"
                        [class.re-disabled]="viewMode"
                        [minDate]="filter.minVal"
                        (onSelect)="changeValidation(index)"
                      ></p-calendar>
                      <div
                        class="re-error mb-2"
                        *ngIf="filter?.error == true && filter?.errorStartDate"
                      >
                        <span> {{ filter?.rangeErrorMsg }} </span>
                      </div>
                    </div>

                    <div class="col-12 col-md-3">
                      <select
                        [(ngModel)]="filter.selectedCondition"
                        class="form-control"
                        [class.re-disabled]="viewMode"
                        (change)="changeValidation(index)"
                      >
                        <ng-container
                          *ngFor="let option of filter?.conditionType"
                        >
                          <option [value]="option.value">
                            {{ option?.label }}
                          </option>
                        </ng-container>
                      </select>
                      <div
                        class="re-error mb-2"
                        *ngIf="filter?.error == true && filter?.conditionError"
                      >
                        {{ filter.rangeErrorMsg }}
                      </div>
                    </div>
                  </ng-container>
                </ng-container>
              </div>
            </div>
          </ng-container>
        </ng-container>
      </div>
      <!-- Columns to Display -->
      <div class="re-columns-title mb-3" *ngIf="isColumnsToDisplay">
        <div class="re-title-text">Columns to Display</div>
      </div>
      <div class="re-valuesdiv" *ngFor="let group of selectedFilterShowGroup">
        <ng-container *ngIf="group?.show">
          <div class="re-checktitle">{{ group?.name }}</div>
        </ng-container>
        <ng-container *ngFor="let filter of selectedFilter; let index = index">
          <ng-container
            *ngIf="
              filter?.filterGroup &&
              group?.name == filter?.filterGroup &&
              filter?.show
            "
          >
            <div class="re-columns-list" *ngIf="filter?.isColumns">
              <div class="w-100 d-inline-block">
                <div class="w-100 d-inline-block">
                  <div class="re-columns-switch">
                    <div class="re-name">{{ filter?.label }}</div>
                    <div class="re-line"></div>
                    <div class="re-switch-main">
                      <label
                        class="re-switch"
                        [class.re-disabled]="viewMode || filter.isDisabled"
                      >
                        <input
                          type="checkbox"
                          name=""
                          [(ngModel)]="filter.isVisible"
                        />
                        <span class="re-slider"></span>
                      </label>
                      <span class="re-switch-text">
                        {{ filter.isVisible ? "Visible" : "Hidden" }}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </ng-container>
        </ng-container>
      </div>
      <div class="re-valuesdiv" *ngIf="selectedFilterGroup.length == 0">
        <ng-container *ngFor="let filter of selectedFilter; let index = index">
          <ng-container *ngIf="!filter?.isColumns">
            <div class="form-group">
              <label class="re-labels">{{ filter?.label }}</label>
              <div class="row">
                <div
                  class="col-12 col-md-4"
                  *ngIf="filter?.value != 'buyerCustomQuestions'"
                >
                  <select
                    class="form-control"
                    [(ngModel)]="filter.selectedOperator"
                    [class.re-disabled]="viewMode"
                    (change)="changeSelectedOperator(filter, index)"
                  >
                    <option
                      *ngFor="let opt of filter?.operator"
                      [value]="opt?.value"
                    >
                      {{ opt?.label }}
                    </option>
                  </select>
                  <div class="re-error mb-2" *ngIf="filter?.operatorError">
                    <span> *Please select one option. </span>
                  </div>
                </div>
                <ng-container [ngSwitch]="filter?.optionsType">
                  <!-- SINGLE SELECT -->
                  <ng-container *ngSwitchCase="'SELECT'">
                    <div class="col-12 col-md-8">
                      <select
                        class="form-control"
                        [(ngModel)]="filter.selectedOption"
                        [class.re-disabled]="viewMode"
                        (change)="changeValidation(index)"
                        [disabled]="
                          data?.fromAudience && filter?.value == 'email'
                        "
                      >
                        <option
                          *ngFor="let option of filter?.options"
                          [value]="option?.value"
                        >
                          {{ option?.label }}
                        </option>
                      </select>
                      <div class="re-error mb-2" *ngIf="filter?.error == true">
                        <span> *Please select one option. </span>
                      </div>
                    </div>
                  </ng-container>

                  <!-- MULTIPLE SELECT -->
                  <ng-container *ngSwitchCase="'MULTI-SELECT'">
                    <div class="col-12 col-md-8">
                      <p-multiSelect
                        #select
                        class="re-custom-field"
                        [name]="filter.value + '-' + index"
                        [id]="filter.value + '-' + index"
                        [placeholder]="'Select ' + filter?.label + '(s)'"
                        [options]="filter.options"
                        [(ngModel)]="filter.selectedOption"
                        appendTo="body"
                        [filter]="true"
                        [filterBy]="'label'"
                        [class.re-disabled]="viewMode"
                        (onChange)="changeValidation(index)"
                      ></p-multiSelect>
                      <div class="re-error mb-2" *ngIf="filter?.error == true">
                        <span> *Please select at least one option. </span>
                      </div>
                    </div>

                    <div
                      [ngClass]="
                        filter?.selectedOperator == 'is'
                          ? 'col-12 col-md-8'
                          : 'col-12 col-md-4'
                      "
                      *ngIf="
                        filter?.selectedOperator == 'is' &&
                        filter?.conditionType &&
                        filter?.conditionType.length
                      "
                    >
                      <select
                        [(ngModel)]="filter.selectedCondition"
                        class="form-control"
                        [class.re-disabled]="viewMode"
                      >
                        <ng-container
                          *ngFor="let option of filter?.conditionType"
                        >
                          <option [value]="option.value">
                            {{ option?.label }}
                          </option>
                        </ng-container>
                      </select>
                    </div>

                    <div
                      [ngClass]="
                        filter?.selectedOperator == 'is-not'
                          ? 'col-12 col-md-8'
                          : 'col-12 col-md-4'
                      "
                      *ngIf="
                        filter?.selectedOperator == 'is-not' &&
                        filter?.conditionType &&
                        filter?.conditionType.length &&
                        (filter.value == 'targetCities' ||
                          filter.value == 'targetZip' ||
                          filter.value == 'targetStates' ||
                          filter.value == 'targetCounty')
                      "
                    >
                      <select
                        [(ngModel)]="filter.selectedCondition"
                        class="form-control"
                        [class.re-disabled]="viewMode"
                      >
                        <ng-container
                          *ngFor="let option of filter?.conditionType"
                        >
                          <option [value]="option.value">
                            {{ option?.label }}
                          </option>
                        </ng-container>
                      </select>
                    </div>
                  </ng-container>

                  <!-- DATE-RANGE -->
                  <!-- only for lead filter -->
                  <ng-container *ngSwitchCase="'DATE-RANGE'">
                    <div
                      [ngClass]="
                        filter?.selectedOperator == 'between'
                          ? 'col-12 col-md-4'
                          : 'col-12 col-md-8'
                      "
                      *ngIf="filter?.selectedOperator != 'is'"
                    >
                      <p-calendar
                        class="re-custom-date"
                        [(ngModel)]="filter.minVal"
                        styleClass="ngCalendarClass"
                        appendTo="body"
                        [showIcon]="true"
                        [icon]="'fa fa-calendar'"
                        placeholder="Start Date"
                        [class.re-disabled]="viewMode"
                        (onSelect)="changeValidation(index)"
                      ></p-calendar>
                      <div
                        class="re-error mb-2"
                        *ngIf="filter?.error == true && filter?.errorStartDate"
                      >
                        <span> {{ filter.rangeErrorMsg }} </span>
                      </div>
                    </div>

                    <div
                      class="col-12 col-md-4"
                      *ngIf="filter?.selectedOperator == 'between'"
                    >
                      <p-calendar
                        class="re-custom-date"
                        [(ngModel)]="filter.maxVal"
                        styleClass="ngCalendarClass"
                        appendTo="body"
                        [showIcon]="true"
                        [icon]="'fa fa-calendar'"
                        placeholder="End Date"
                        [minDate]="filter.minVal"
                        [class.re-disabled]="viewMode"
                        (onSelect)="changeValidation(index)"
                      ></p-calendar>
                      <div
                        class="re-error mb-2"
                        *ngIf="filter?.error == true && filter?.errorEndDate"
                      >
                        <span> {{ filter.rangeErrorMsg }} </span>
                      </div>
                    </div>

                    <div
                      [ngClass]="
                        filter?.selectedOperator == 'is'
                          ? 'col-12 col-md-8'
                          : 'col-12 col-md-4'
                      "
                      *ngIf="
                        filter?.selectedOperator == 'is' &&
                        filter?.conditionType &&
                        filter?.conditionType.length
                      "
                    >
                      <select
                        [(ngModel)]="filter.selectedCondition"
                        class="form-control"
                        [class.re-disabled]="viewMode"
                      >
                        <ng-container
                          *ngFor="let option of filter?.conditionType"
                        >
                          <option [value]="option.value">
                            {{ option?.label }}
                          </option>
                        </ng-container>
                      </select>
                    </div>
                  </ng-container>

                  <!-- RANGE -->
                  <ng-container *ngSwitchCase="'RANGE'">
                    <div
                      [ngClass]="
                        filter?.selectedOperator == 'between'
                          ? 'col-12 col-md-4'
                          : 'col-12 col-md-8'
                      "
                    >
                      <input
                        class="form-control"
                        type="number"
                        placeholder="{{
                          filter?.selectedOperator == 'between'
                            ? 'Min'
                            : 'Value'
                        }}"
                        min="0"
                        [(ngModel)]="filter.minVal"
                        [class.re-disabled]="viewMode"
                        onkeypress="return (event.charCode == 8) ? null : event.charCode >= 48 && event.charCode <= 57"
                        (change)="changeValidation(index)"
                      />
                      <div
                        class="re-error mb-2"
                        *ngIf="filter?.error == true && filter?.errorMinVal"
                      >
                        {{ filter.rangeErrorMsg }}
                      </div>
                    </div>
                    <div
                      class="col-12 col-md-4"
                      *ngIf="filter?.selectedOperator == 'between'"
                    >
                      <input
                        class="form-control"
                        type="number"
                        placeholder="Max"
                        min="0"
                        [(ngModel)]="filter.maxVal"
                        [class.re-disabled]="viewMode"
                        onkeypress="return (event.charCode == 8) ? null : event.charCode >= 48 && event.charCode <= 57"
                        (change)="changeValidation(index)"
                      />
                      <div
                        class="re-error mb-2"
                        *ngIf="filter?.error == true && filter?.errorMaxVal"
                      >
                        {{ filter.rangeErrorMsg }}
                      </div>
                    </div>
                  </ng-container>

                  <!-- TIME-RANGE -->
                  <ng-container *ngSwitchCase="'TIME-RANGE'">
                    <div class="col-12 col-md-4">
                      <input
                        class="form-control"
                        type="number"
                        placeholder="{{
                          filter?.selectedOperator == 'between'
                            ? 'Min'
                            : 'Value'
                        }}"
                        min="0"
                        [(ngModel)]="filter.selectedOption"
                        [class.re-disabled]="viewMode"
                        onkeypress="return (event.charCode == 8) ? null : event.charCode >= 48 && event.charCode <= 57"
                        (change)="changeValidation(index)"
                      />
                      <div
                        class="re-error mb-2"
                        *ngIf="filter?.error == true && filter?.errorMinVal"
                      >
                        {{ filter.rangeErrorMsg }}
                      </div>
                    </div>

                    <div class="col-12 col-md-4">
                      <select
                        [(ngModel)]="filter.selectedCondition"
                        class="form-control"
                        [class.re-disabled]="viewMode"
                        (change)="changeValidation(index)"
                      >
                        <ng-container
                          *ngFor="let option of filter?.conditionType"
                        >
                          <option [value]="option.value">
                            {{ option?.label }}
                          </option>
                        </ng-container>
                      </select>
                      <div
                        class="re-error mb-2"
                        *ngIf="filter?.error == true && filter?.conditionError"
                      >
                        {{ filter.rangeErrorMsg }}
                      </div>
                    </div>
                  </ng-container>
                  <!-- CUSTOM QUESTION -->
                  <ng-container *ngSwitchCase="'QUESTION'">
                    <ng-container
                      *ngFor="
                        let master of filter.masterOptions;
                        let index = index
                      "
                    >
                      <div class="form-group col-12">
                        <label class="re-labels">{{ master?.label }}</label>
                        <div class="row">
                          <div class="col-12 col-md-4">
                            <select
                              class="form-control"
                              [(ngModel)]="master.selectedOperator"
                              [class.re-disabled]="viewMode"
                            >
                              <option
                                *ngFor="let opt of master?.operator"
                                [value]="opt?.value"
                              >
                                {{ opt?.label }}
                              </option>
                            </select>
                          </div>

                          <ng-container [ngSwitch]="master?.type">
                            <!-- CHECK_LIST -->
                            <ng-container *ngSwitchCase="'CHECK_LIST'">
                              <div class="col-12 col-md-8">
                                <p-multiSelect
                                  #select
                                  class="re-custom-field"
                                  [name]="master.value + '-' + index"
                                  [id]="master.value + '-' + index"
                                  [placeholder]="
                                    'Select ' + master?.label + '(s)'
                                  "
                                  [options]="master.options"
                                  [(ngModel)]="master.selectedOption"
                                  appendTo="body"
                                  [virtualScroll]="true"
                                  [virtualScrollItemSize]="43"
                                  [class.re-disabled]="viewMode"
                                ></p-multiSelect>
                              </div>
                            </ng-container>

                            <!-- RADIO_BUTTON -->
                            <ng-container *ngSwitchCase="'RADIO_BUTTON'">
                              <div class="col-12 col-md-8">
                                <select
                                  class="form-control"
                                  [(ngModel)]="master.selectedOption"
                                  [class.re-disabled]="viewMode"
                                >
                                  <option
                                    *ngFor="let option of master?.options"
                                    [value]="option?.value"
                                  >
                                    {{ option?.label }}
                                  </option>
                                </select>
                              </div>
                            </ng-container>

                            <!-- DROPDOWN -->
                            <ng-container *ngSwitchCase="'DROPDOWN'">
                              <!-- <div class="col-12 col-md-8">
                          <p-multiSelect
                            #select
                            class="re-custom-field"
                            [name]="master.value + '-' + index"
                            [id]="master.value + '-' + index"
                            [placeholder]="
                              'Select ' + master?.label + '(s)'
                            "
                            [options]="master.options"
                            [(ngModel)]="master.selectedOption"
                            appendTo="body"
                            [virtualScroll]="true"
                            [virtualScrollItemSize]="43"
                            [class.re-disabled]="viewMode"
                          ></p-multiSelect>
                        </div> -->

                              <div class="col-12 col-md-8">
                                <select
                                  class="form-control"
                                  [(ngModel)]="master.selectedOption"
                                  [class.re-disabled]="viewMode"
                                >
                                  <option
                                    *ngFor="let option of master?.options"
                                    [value]="option?.value"
                                  >
                                    {{ option?.label }}
                                  </option>
                                </select>
                              </div>
                            </ng-container>

                            <!-- NUMBER -->
                            <ng-container *ngSwitchCase="'NUMBER'">
                              <div
                                [ngClass]="
                                  master?.selectedOperator == 'between'
                                    ? 'col-12 col-md-4'
                                    : 'col-12 col-md-8'
                                "
                              >
                                <input
                                  class="form-control"
                                  type="number"
                                  placeholder="{{
                                    master?.selectedOperator == 'between'
                                      ? 'Min'
                                      : 'Value'
                                  }}"
                                  min="0"
                                  [(ngModel)]="master.minVal"
                                  [class.re-disabled]="viewMode"
                                  onkeypress="return (event.charCode == 8) ? null : event.charCode >= 48 && event.charCode <= 57"
                                />
                              </div>
                              <div
                                class="col-12 col-md-4"
                                *ngIf="master?.selectedOperator == 'between'"
                              >
                                <input
                                  class="form-control"
                                  type="number"
                                  placeholder="Max"
                                  min="0"
                                  [(ngModel)]="master.maxVal"
                                  [class.re-disabled]="viewMode"
                                  onkeypress="return (event.charCode == 8) ? null : event.charCode >= 48 && event.charCode <= 57"
                                />
                              </div>
                            </ng-container>

                            <!-- CURRENCY -->
                            <ng-container *ngSwitchCase="'CURRENCY'">
                              <div
                                [ngClass]="
                                  master?.selectedOperator == 'between'
                                    ? 'col-12 col-md-4'
                                    : 'col-12 col-md-8'
                                "
                              >
                                <input
                                  class="form-control"
                                  type="number"
                                  placeholder="{{
                                    master?.selectedOperator == 'between'
                                      ? 'Min'
                                      : 'Value'
                                  }}"
                                  min="0"
                                  [(ngModel)]="master.minVal"
                                  [class.re-disabled]="viewMode"
                                  onkeypress="return (event.charCode == 8) ? null : event.charCode >= 48 && event.charCode <= 57"
                                />
                              </div>
                              <div
                                class="col-12 col-md-4"
                                *ngIf="master?.selectedOperator == 'between'"
                              >
                                <input
                                  class="form-control"
                                  type="number"
                                  placeholder="Max"
                                  min="0"
                                  [(ngModel)]="master.maxVal"
                                  [class.re-disabled]="viewMode"
                                  onkeypress="return (event.charCode == 8) ? null : event.charCode >= 48 && event.charCode <= 57"
                                />
                              </div>
                            </ng-container>

                            <!-- TEXT -->
                            <ng-container *ngSwitchCase="'TEXT'">
                              <!-- <div class="col-12 col-md-8">
                          <input
                            class="form-control"
                            type="text"
                            placeholder="Write answer"
                            [(ngModel)]="master.selectedOption"
                            [class.re-disabled]="viewMode"
                          />
                        </div> -->

                              <div class="col-12 col-md-8">
                                <select
                                  class="form-control"
                                  [(ngModel)]="master.selectedOption"
                                  [class.re-disabled]="viewMode"
                                >
                                  <option
                                    *ngFor="let option of master?.options"
                                    [value]="option?.value"
                                  >
                                    {{ option?.label }}
                                  </option>
                                </select>
                              </div>
                            </ng-container>

                            <!-- MULTILINE_TEXT -->
                            <ng-container *ngSwitchCase="'MULTILINE_TEXT'">
                              <!-- <div class="col-12 col-md-8">
                          <textarea
                            class="form-control"
                            type="text"
                            placeholder="Write answer"
                            [(ngModel)]="master.selectedOption"
                            [class.re-disabled]="viewMode"
                          ></textarea>
                        </div> -->

                              <div class="col-12 col-md-8">
                                <select
                                  class="form-control"
                                  [(ngModel)]="master.selectedOption"
                                  [class.re-disabled]="viewMode"
                                >
                                  <option
                                    *ngFor="let option of master?.options"
                                    [value]="option?.value"
                                  >
                                    {{ option?.label }}
                                  </option>
                                </select>
                              </div>
                            </ng-container>

                            <!-- DATE -->
                            <ng-container *ngSwitchCase="'DATE'">
                              <div
                                [ngClass]="
                                  master?.selectedOperator == 'between'
                                    ? 'col-12 col-md-4'
                                    : 'col-12 col-md-8'
                                "
                                *ngIf="master?.selectedOperator != 'is'"
                              >
                                <p-calendar
                                  class="re-custom-date"
                                  [(ngModel)]="master.minVal"
                                  styleClass="ngCalendarClass"
                                  appendTo="body"
                                  [showIcon]="true"
                                  [icon]="'fa fa-calendar'"
                                  placeholder="Start Date"
                                  [class.re-disabled]="viewMode"
                                ></p-calendar>
                              </div>

                              <div
                                class="col-12 col-md-4"
                                *ngIf="master?.selectedOperator == 'between'"
                              >
                                <p-calendar
                                  class="re-custom-date"
                                  [(ngModel)]="master.maxVal"
                                  styleClass="ngCalendarClass"
                                  appendTo="body"
                                  [showIcon]="true"
                                  [icon]="'fa fa-calendar'"
                                  placeholder="End Date"
                                  [minDate]="master.minVal"
                                  [class.re-disabled]="viewMode"
                                ></p-calendar>
                              </div>
                            </ng-container>
                          </ng-container>
                        </div>
                      </div>
                    </ng-container>
                  </ng-container>
                </ng-container>
              </div>
            </div>
          </ng-container>
        </ng-container>
      </div>
    </mat-dialog-content>
  </div>

  <div class="re-configure-div" *ngIf="step3 && !isShare">
    <mat-dialog-content>
      <div class="re-switchbox" *ngIf="!data?.fromAudience">
        <h4 class="re-text1">Would like to save this filter?</h4>
        <label
          class="re-switch"
          [class.re-disabled]="selectedFilter.length == 0 || viewMode"
        >
          <input
            type="checkbox"
            [(ngModel)]="isSaveFilter"
            [class.re-disabled]="viewMode"
          />
          <span class="re-slider"></span>
        </label>
        <div class="re-desq">
          Saved filters are time saver. Let your desired team members Quickly
          filter the important data on a single click.
        </div>
      </div>
      <div
        class="re-sharingbox"
        [class.re-disabled]="
          !isSaveFilter && data?.moduleName !== 'Audience List'
        "
      >
        <div class="mb-3">
          <form [formGroup]="filterDetailForm" (onsubmit)="applyFilter()">
            <label class="re-labels"
              >Filter Name and Description<span class="text-danger"
                >*</span
              ></label
            >
            <input
              type="text"
              class="form-control"
              placeholder="Enter name"
              formControlName="filterTitle"
              [class.re-disabled]="selectedFilter.length == 0 || viewMode"
            />
            <div
              class="re-error"
              *ngIf="
                (filterTitle.dirty || filterTitle.touched || isSubmitted) &&
                filterTitle.hasError('required')
              "
            >
              {{ messageConstant.requiredName.replace("Name", "Filter Name") }}
            </div>
            <div
              class="re-error"
              *ngIf="
                (filterTitle.dirty || filterTitle.touched || isSubmitted) &&
                filterTitle.hasError('pattern')
              "
            >
              Name can not start or end with a space.
            </div>
            <textarea
              class="form-control mt-3"
              rows="2"
              placeholder="Enter description"
              formControlName="filterDescription"
              [class.re-disabled]="selectedFilter.length == 0 || viewMode"
            ></textarea>
          </form>
        </div>
        <div
          class="mb-2"
          [class.re-disabled]="selectedFilter.length == 0 || viewMode"
          *ngIf="!data?.isReturnFilterData"
        >
          <label class="re-labels">Sharing, Saving, and Personalising</label>
          <div class="mb-3">
            <div class="re-sharinglist">
              <div class="re-sharebtn">
                <img
                  class="mr-1"
                  width="20"
                  src="assets/images/quick-filters/user-black.svg"
                  alt=""
                />
                Sharing
              </div>
              <div class="re-lists" *ngIf="isUserView">
                <ng-container *ngFor="let tag of userPermissionMain">
                  <img
                    *ngIf="tag?.profileImage"
                    alt="profile"
                    [src]="tag?.profileImage"
                  />
                  <span *ngIf="!tag?.profileImage"
                    >{{ getInitials(tag?.name) }}
                  </span>
                </ng-container>
              </div>
              <div class="re-texticon" *ngIf="!isUserView">
                Select team members to share
              </div>
              <a class="re-links" (click)="manageAction('share')">Manage</a>
            </div>
            <div
              class="re-error"
              *ngIf="isSubmitted && userPermission.length == 0"
            >
              "Please select one permission"
            </div>
          </div>

          <div class="mb-3" [class.re-disabled]="viewMode || isEdit">
            <div id="re-select-field" class="re-customdrop">
              <div class="re-sharinglist" (click)="showMenu()">
                <div class="re-sharebtn">
                  <img
                    class="mr-1"
                    width="20"
                    src="assets/images/quick-filters/save-black.svg"
                    alt=""
                  />
                  Saving
                </div>
                <div
                  class="re-texticon"
                  *ngIf="isObjectEmpty(selectedFolderData)"
                >
                  Select location to save
                </div>
                <div
                  class="re-texticon"
                  *ngIf="!isObjectEmpty(selectedFolderData)"
                >
                  {{ selectedFolderData?.title }}
                </div>
                <a
                  class="re-links"
                  [ngClass]="isMenu ? 'fa fa-angle-up' : 'fa fa-angle-down'"
                ></a>
              </div>
              <div class="dropdown-menu w-100 p-0" [class.show]="isMenu">
                <div class="re-folder-search">
                  <div class="re-search-div">
                    <i class="fa fa-search"></i>
                    <input
                      #search
                      type="text"
                      class="form-control"
                      id="searchInputThree"
                      placeholder="Search folder"
                      [(ngModel)]="fieldFolder"
                      (input)="searchFiltersFour()"
                    />
                  </div>
                  <a class="re-links" (click)="selectFolder(individualObj)"
                    >Save as an Individual Filter</a
                  >
                </div>
                <div class="re-folder-list">
                  <ng-container *ngFor="let item of folderList">
                    <a class="re-folders" (click)="selectFolder(item)">{{
                      item?.title
                    }}</a>
                  </ng-container>
                </div>
              </div>
            </div>
            <div
              class="re-error"
              *ngIf="isSubmitted && isObjectEmpty(selectedFolderData)"
            >
              "Please select location of the filter"
            </div>
          </div>

          <div class="mb-3 d-none">
            <div class="re-sharinglist">
              <div class="re-sharebtn">
                <img
                  class="mr-1"
                  width="20"
                  src="assets/images/quick-filters/column-black.svg"
                  alt=""
                />
                Columns
              </div>
              <div class="re-texticon">Select columns to show</div>
              <a class="re-links">Manage</a>
            </div>
          </div>
        </div>
      </div>
    </mat-dialog-content>
  </div>

  <!-- <div *ngIf="isShare">
    <div class="re-sharehead-div">
      <h3>
        Share Permission
        <span
          >of “{{
            filterDetailForm.value.filterTitle
              ? filterDetailForm.value.filterTitle
              : "N/a"
          }}”</span
        >
      </h3>
    </div>
    <div class="re-permission-div">
      <div class="re-search-div">
        <i class="fa fa-search"></i>
        <p-autoComplete
          [(ngModel)]="userFind"
          [suggestions]="userList"
          (completeMethod)="searchFiltersThree($event)"
          field="name"
          (onSelect)="selectUser($event)"
        ></p-autoComplete>
      </div>
      <mat-dialog-content>
        <div class="re-shareuser-div">
          <div class="re-100 mb-2">
            <div class="re-60">
              <div class="re-share-titles">Team Members</div>
            </div>
            <div class="re-40">
              <div class="re-share-titles">Permission</div>
            </div>
          </div>
          <div class="re-100">
            <div
              class="re-100 mb-3 re-permission-hover"
              *ngFor="let item of userPermissionMain; let i = index"
            >
              <div class="re-60">
                <div class="re-userinfo">
                  <div
                    class="re-delete"
                    *ngIf="item._id != _commonFunctionService.userData._id"
                    (click)="deleteUser(item)"
                  >
                    <img src="assets/images/delete-red1.svg" alt="delete" />
                  </div>
                  <div class="re-short">{{ getInitials(item?.name) }}</div>
                  <div class="re-names">
                    {{
                      item?.name
                        ? _commonFunctionService.capitalizeName(item?.name)
                        : "N/a"
                    }}
                  </div>
                  <div class="re-dates">Since Jan 2, 2024</div>
                </div>
              </div>
              <div class="re-40">
                <div class="re-radiolist">
                  <mat-radio-group
                    aria-labelledby="example-radio-group-label"
                    class="example-radio-group"
                    [class.re-disabled]="
                      viewMode ||
                      item._id == _commonFunctionService.userData._id
                    "
                    [(ngModel)]="item.permission"
                    (change)="checkedUser($event, item)"
                  >
                    <mat-radio-button
                      class="example-radio-button mr-2"
                      [value]="1"
                      >Edit</mat-radio-button
                    >
                    <mat-radio-button class="example-radio-button" [value]="2"
                      >View</mat-radio-button
                    >
                  </mat-radio-group>
                </div>
              </div>
            </div>
          </div>
        </div>
      </mat-dialog-content>
    </div>
  </div> -->

  <div *ngIf="isShare">
    <div class="re-sharehead-div">
      <h3>
        Share Permission
        <span
          >of “{{
            filterDetailForm.value.filterTitle
              ? filterDetailForm.value.filterTitle
              : "N/a"
          }}”</span
        >
      </h3>
    </div>
    <div class="re-permission-div">
      <div class="re-search-div">
        <i class="fa fa-search"></i>
        <input
          #search
          type="text"
          class="form-control"
          name=""
          id="searchInputFor"
          placeholder="Search filter users"
          [(ngModel)]="userFind"
          (input)="searchFiltersThree()"
        />
      </div>
      <mat-dialog-content>
        <div class="re-shareuser-div">
          <div class="re-100 mb-2">
            <div class="re-50">
              <div class="re-share-titles">Team Members</div>
            </div>
            <div class="re-50">
              <div class="re-share-titles">Permission</div>
            </div>
          </div>
          <div class="re-100">
            <div
              class="re-100 mb-3 re-permission-hover"
              *ngFor="let item of userList; let i = index"
            >
              <div class="re-50">
                <div class="re-userinfo">
                  <!-- <div
                    class="re-delete"
                    *ngIf="item._id != _commonFunctionService.userData._id"
                    (click)="deleteUser(item)"
                  >
                    <img src="assets/images/delete-red1.svg" alt="delete" />
                  </div> -->
                  <div class="re-short" *ngIf="!item.profileImage">
                    {{ getInitials(item?.name) }}
                  </div>
                  <div class="re-short" *ngIf="item.profileImage">
                    <img alt="profile" [src]="item?.profileImage" />
                  </div>
                  <div class="re-names">
                    {{
                      item?.name
                        ? _commonFunctionService.capitalizeName(item?.name)
                        : "N/a"
                    }}
                  </div>
                  <div class="re-dates">
                    Since {{ item.createdAt | date : "MMM dd, yyyy" }}
                  </div>
                </div>
              </div>
              <div class="re-50">
                <div class="re-radiolist">
                  <mat-radio-group
                    aria-labelledby="example-radio-group-label"
                    class="example-radio-group"
                    [class.re-disabled]="
                      viewMode ||
                      item._id == userData?._id ||
                      item._id == this.data?.filterRecord?.createdBy
                    "
                    [(ngModel)]="item.permission"
                    (change)="checkedUser($event, item)"
                  >
                    <mat-radio-button
                      class="example-radio-button mr-2"
                      [value]="3"
                      >None</mat-radio-button
                    >
                    <mat-radio-button
                      class="example-radio-button mr-2"
                      [value]="2"
                      >View</mat-radio-button
                    >
                    <mat-radio-button class="example-radio-button" [value]="1"
                      >Edit</mat-radio-button
                    >
                  </mat-radio-group>
                </div>
              </div>
            </div>
          </div>
        </div>
      </mat-dialog-content>
    </div>
  </div>

  <button
    type="button"
    class="btn btn-block btn-save"
    (click)="
      tabIndex == 3 && !isShare
        ? applyFilter()
        : tabIndex == 3 && isShare
        ? backToApplyFilter()
        : continueToNext()
    "
    [class.re-disabled]="tabIndex == 3 && selectedFilter.length == 0"
  >
    {{
      tabIndex === 3 && isSaveFilter && !isShare
        ? data?.isReturnFilterData
          ? (isEdit ? "Update " : "Save ") + data?.moduleName
          : "Save and Apply"
        : tabIndex === 3 && !isSaveFilter && !isShare
        ? "Apply Filter"
        : tabIndex === 3 && !isSaveFilter && isShare
        ? "Save Sharing Preferences"
        : "Continue"
    }}
  </button>
</div>
